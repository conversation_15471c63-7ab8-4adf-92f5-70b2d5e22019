import 'package:conduit/core/errors/failures.dart';
import 'package:conduit/core/use_case/use_case.dart';
import 'package:conduit/features/auth/domain/entities/user_entity.dart';
import 'package:conduit/features/auth/domain/repositories/auth_repository.dart';
import 'package:dartz/dartz.dart';
import 'package:injectable/injectable.dart';

@injectable
class SignInWithGoogleUseCase implements UseCase<UserEntity, NoParams> {
  final AuthRepository authRepository;

  SignInWithGoogleUseCase(this.authRepository);

  @override
  Future<Either<Failure, UserEntity>> call(NoParams params) async {
    return await authRepository.signInWithGoogle();
  }
}

import 'package:conduit/core/di/service_locator.dart';
import 'package:conduit/features/auth/presentation/bloc/signup/signup_bloc.dart';
import 'package:conduit/features/auth/presentation/bloc/signup/signup_event.dart';
import 'package:conduit/features/auth/presentation/bloc/signup/signup_state.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class SignUpPage extends StatelessWidget {
  const SignUpPage({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => getIt<SignUpBloc>(),
      child: const SignUpView(),
    );
  }
}

class SignUpView extends StatelessWidget {
  const SignUpView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Theme.of(context).colorScheme.surface,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: Theme.of(context).colorScheme.onSurface,
          ),
          onPressed: () => Navigator.of(context).pop(),
        ),
      ),
      body: BlocListener<SignUpBloc, SignUpState>(
        listener: (context, state) {
          if (state.status == SignUpStatus.failure) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.errorMessage ?? 'Sign up failed'),
                backgroundColor: Theme.of(context).colorScheme.error,
              ),
            );
          } else if (state.status == SignUpStatus.success) {
            Navigator.of(
              context,
            ).pushNamedAndRemoveUntil('/home', (route) => false);
          }
        },
        child: SafeArea(
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(24.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                const SizedBox(height: 20),
                _buildHeader(context),
                const SizedBox(height: 40),
                _buildSignUpForm(context),
                const SizedBox(height: 32),
                _buildSocialSignUp(context),
                const SizedBox(height: 24),
                _buildBottomLinks(context),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Column(
      children: [
        Icon(
          Icons.person_add_outlined,
          size: 80,
          color: Theme.of(context).colorScheme.primary,
        ),
        const SizedBox(height: 24),
        Text(
          'Create Account',
          style: Theme.of(context).textTheme.headlineMedium?.copyWith(
            fontWeight: FontWeight.bold,
            color: Theme.of(context).colorScheme.onSurface,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          'Sign up to get started',
          style: Theme.of(context).textTheme.bodyLarge?.copyWith(
            color: Theme.of(context).colorScheme.onSurfaceVariant,
          ),
        ),
      ],
    );
  }

  Widget _buildSignUpForm(BuildContext context) {
    return BlocBuilder<SignUpBloc, SignUpState>(
      builder: (context, state) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            _buildFullNameField(context, state),
            const SizedBox(height: 20),
            _buildEmailField(context, state),
            const SizedBox(height: 20),
            _buildPasswordField(context, state),
            const SizedBox(height: 20),
            _buildConfirmPasswordField(context, state),
            const SizedBox(height: 32),
            _buildSignUpButton(context, state),
          ],
        );
      },
    );
  }

  Widget _buildFullNameField(BuildContext context, SignUpState state) {
    return TextFormField(
      onChanged: (name) =>
          context.read<SignUpBloc>().add(SignUpNameChanged(name)),
      textCapitalization: TextCapitalization.words,
      textInputAction: TextInputAction.next,
      style: const TextStyle(fontSize: 16),
      decoration: InputDecoration(
        labelText: 'Name',
        prefixIcon: const Icon(Icons.person_outlined, size: 24),
        border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
        contentPadding: const EdgeInsets.symmetric(
          horizontal: 16,
          vertical: 20,
        ),
        errorText: state.name.isNotEmpty && !state.isNameValid
            ? 'Please enter your name'
            : null,
      ),
    );
  }

  Widget _buildEmailField(BuildContext context, SignUpState state) {
    return TextFormField(
      onChanged: (email) =>
          context.read<SignUpBloc>().add(SignUpEmailChanged(email)),
      keyboardType: TextInputType.emailAddress,
      textInputAction: TextInputAction.next,
      style: const TextStyle(fontSize: 16),
      decoration: InputDecoration(
        labelText: 'Email',
        prefixIcon: const Icon(Icons.email_outlined, size: 24),
        border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
        contentPadding: const EdgeInsets.symmetric(
          horizontal: 16,
          vertical: 20,
        ),
        errorText: state.email.isNotEmpty && !state.isEmailValid
            ? 'Please enter a valid email'
            : null,
      ),
    );
  }

  Widget _buildPasswordField(BuildContext context, SignUpState state) {
    return TextFormField(
      onChanged: (password) =>
          context.read<SignUpBloc>().add(SignUpPasswordChanged(password)),
      obscureText: !state.isPasswordVisible,
      textInputAction: TextInputAction.next,
      style: const TextStyle(fontSize: 16),
      decoration: InputDecoration(
        labelText: 'Password',
        prefixIcon: const Icon(Icons.lock_outlined, size: 24),
        suffixIcon: IconButton(
          icon: Icon(
            state.isPasswordVisible ? Icons.visibility_off : Icons.visibility,
            size: 24,
          ),
          onPressed: () =>
              context.read<SignUpBloc>().add(SignUpPasswordVisibilityToggled()),
        ),
        border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
        contentPadding: const EdgeInsets.symmetric(
          horizontal: 16,
          vertical: 20,
        ),
        errorText: state.password.isNotEmpty && !state.isPasswordValid
            ? 'Password must be at least 6 characters'
            : null,
        helperText: 'At least 6 characters',
      ),
    );
  }

  Widget _buildConfirmPasswordField(BuildContext context, SignUpState state) {
    return TextFormField(
      onChanged: (confirmPassword) => context.read<SignUpBloc>().add(
        SignUpConfirmPasswordChanged(confirmPassword),
      ),
      obscureText: !state.isConfirmPasswordVisible,
      textInputAction: TextInputAction.done,
      style: const TextStyle(fontSize: 16),
      decoration: InputDecoration(
        labelText: 'Confirm Password',
        prefixIcon: const Icon(Icons.lock_outlined, size: 24),
        suffixIcon: IconButton(
          icon: Icon(
            state.isConfirmPasswordVisible
                ? Icons.visibility_off
                : Icons.visibility,
            size: 24,
          ),
          onPressed: () => context.read<SignUpBloc>().add(
            SignUpConfirmPasswordVisibilityToggled(),
          ),
        ),
        border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
        contentPadding: const EdgeInsets.symmetric(
          horizontal: 16,
          vertical: 20,
        ),
        errorText:
            state.confirmPassword.isNotEmpty && !state.isConfirmPasswordValid
            ? 'Passwords do not match'
            : null,
      ),
    );
  }

  Widget _buildSignUpButton(BuildContext context, SignUpState state) {
    final isLoading = state.status == SignUpStatus.loading;
    final canSubmit = state.isFormValid && !isLoading;

    return ElevatedButton(
      onPressed: canSubmit
          ? () => context.read<SignUpBloc>().add(SignUpSubmitted())
          : null,
      style: ElevatedButton.styleFrom(
        padding: const EdgeInsets.symmetric(vertical: 18),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        textStyle: const TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
      ),
      child: isLoading
          ? const SizedBox(
              height: 20,
              width: 20,
              child: CircularProgressIndicator(strokeWidth: 2),
            )
          : const Text('Sign Up'),
    );
  }

  Widget _buildSocialSignUp(BuildContext context) {
    return BlocBuilder<SignUpBloc, SignUpState>(
      builder: (context, state) {
        final isLoading = state.status == SignUpStatus.loading;

        return Column(
          children: [
            Row(
              children: [
                Expanded(
                  child: Divider(color: Theme.of(context).colorScheme.outline),
                ),
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  child: Text(
                    'Or sign up with',
                    style: TextStyle(
                      color: Theme.of(context).colorScheme.onSurfaceVariant,
                      fontSize: 14,
                    ),
                  ),
                ),
                Expanded(
                  child: Divider(color: Theme.of(context).colorScheme.outline),
                ),
              ],
            ),
            const SizedBox(height: 24),
            Row(
              children: [
                Expanded(
                  child: _buildSocialButton(
                    context: context,
                    icon: Icons.g_mobiledata,
                    label: 'Google',
                    onPressed: isLoading
                        ? null
                        : () => context.read<SignUpBloc>().add(
                            SignUpWithGoogle(),
                          ),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: _buildSocialButton(
                    context: context,
                    icon: Icons.apple,
                    label: 'Apple',
                    onPressed: isLoading
                        ? null
                        : () =>
                              context.read<SignUpBloc>().add(SignUpWithApple()),
                  ),
                ),
              ],
            ),
          ],
        );
      },
    );
  }

  Widget _buildSocialButton({
    required BuildContext context,
    required IconData icon,
    required String label,
    required VoidCallback? onPressed,
  }) {
    return OutlinedButton.icon(
      onPressed: onPressed,
      icon: Icon(icon, size: 24),
      label: Text(label),
      style: OutlinedButton.styleFrom(
        padding: const EdgeInsets.symmetric(vertical: 16),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        textStyle: const TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
      ),
    );
  }

  Widget _buildBottomLinks(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Text(
          'Already have an account? ',
          style: TextStyle(
            color: Theme.of(context).colorScheme.onSurfaceVariant,
            fontSize: 14,
          ),
        ),
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: Text(
            'Sign In',
            style: TextStyle(
              color: Theme.of(context).colorScheme.primary,
              fontSize: 14,
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
      ],
    );
  }
}

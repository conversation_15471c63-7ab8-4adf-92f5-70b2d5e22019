import 'package:conduit/core/use_case/use_case.dart';
import 'package:conduit/features/auth/domain/use_cases/sign_in_with_apple_use_case.dart';
import 'package:conduit/features/auth/domain/use_cases/sign_in_with_google_use_case.dart';
import 'package:conduit/features/auth/domain/use_cases/sign_up_with_email_and_password_use_case.dart';
import 'package:injectable/injectable.dart';

import 'signup_event.dart';
import 'signup_state.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

@injectable
class SignUpBloc extends Bloc<SignUpEvent, SignUpState> {
  final SignUpWithEmailAndPasswordUseCase _signUpWithEmailAndPasswordUseCase;
  final SignInWithAppleUseCase _signInWithAppleUseCase;
  final SignInWithGoogleUseCase _signInWithGoogleUseCase;

  SignUpBloc({
    required SignUpWithEmailAndPasswordUseCase
    signUpWithEmailAndPasswordUseCase,
    required SignInWithAppleUseCase signInWithAppleUseCase,
    required SignInWithGoogleUseCase signInWithGoogleUseCase,
  }) : _signInWithAppleUseCase = signInWithAppleUseCase,
       _signInWithGoogleUseCase = signInWithGoogleUseCase,
       _signUpWithEmailAndPasswordUseCase = signUpWithEmailAndPasswordUseCase,
       super(const SignUpState()) {
    on<SignUpEmailChanged>(_onEmailChanged);
    on<SignUpPasswordChanged>(_onPasswordChanged);
    on<SignUpConfirmPasswordChanged>(_onConfirmPasswordChanged);
    on<SignUpNameChanged>(_onFullNameChanged);
    on<SignUpSubmitted>(_onSubmitted);
    on<SignUpWithGoogle>(_onSignUpWithGoogle);
    on<SignUpWithApple>(_onSignUpWithApple);
    on<SignUpPasswordVisibilityToggled>(_onPasswordVisibilityToggled);
    on<SignUpConfirmPasswordVisibilityToggled>(
      _onConfirmPasswordVisibilityToggled,
    );
  }

  void _onEmailChanged(SignUpEmailChanged event, Emitter<SignUpState> emit) {
    final isEmailValid = _isValidEmail(event.email);
    emit(
      state.copyWith(
        email: event.email,
        isEmailValid: isEmailValid,
        status: SignUpStatus.initial,
      ),
    );
  }

  void _onPasswordChanged(
    SignUpPasswordChanged event,
    Emitter<SignUpState> emit,
  ) {
    final isPasswordValid = event.password.length >= 6;
    final isConfirmPasswordValid = event.password == state.confirmPassword;
    emit(
      state.copyWith(
        password: event.password,
        isPasswordValid: isPasswordValid,
        isConfirmPasswordValid: isConfirmPasswordValid,
        status: SignUpStatus.initial,
      ),
    );
  }

  void _onConfirmPasswordChanged(
    SignUpConfirmPasswordChanged event,
    Emitter<SignUpState> emit,
  ) {
    final isConfirmPasswordValid =
        event.confirmPassword == state.password &&
        event.confirmPassword.isNotEmpty;
    emit(
      state.copyWith(
        confirmPassword: event.confirmPassword,
        isConfirmPasswordValid: isConfirmPasswordValid,
        status: SignUpStatus.initial,
      ),
    );
  }

  void _onFullNameChanged(SignUpNameChanged event, Emitter<SignUpState> emit) {
    final isNameValid = event.name.trim().length >= 2;
    emit(
      state.copyWith(
        name: event.name,
        isNameValid: isNameValid,
        status: SignUpStatus.initial,
      ),
    );
  }

  Future<void> _onSubmitted(
    SignUpSubmitted event,
    Emitter<SignUpState> emit,
  ) async {
    if (!state.isFormValid) return;

    emit(state.copyWith(status: SignUpStatus.loading));

    final result = await _signUpWithEmailAndPasswordUseCase.call(
      Params(email: state.email, password: state.password, name: state.name),
    );

    result.fold(
      (failure) => emit(state.copyWith(status: SignUpStatus.failure)),
      (userEntity) async {
        emit(state.copyWith(status: SignUpStatus.success));
      },
    );
  }

  Future<void> _onSignUpWithGoogle(
    SignUpWithGoogle event,
    Emitter<SignUpState> emit,
  ) async {
    emit(state.copyWith(status: SignUpStatus.loading));

    final result = await _signInWithGoogleUseCase.call(NoParams());
    result.fold(
      (failure) => emit(state.copyWith(status: SignUpStatus.failure)),
      (userEntity) async {
        emit(state.copyWith(status: SignUpStatus.success));
      },
    );
  }

  Future<void> _onSignUpWithApple(
    SignUpWithApple event,
    Emitter<SignUpState> emit,
  ) async {
    emit(state.copyWith(status: SignUpStatus.loading));

    final result = await _signInWithAppleUseCase.call(NoParams());
    result.fold(
      (failure) => emit(state.copyWith(status: SignUpStatus.failure)),
      (userEntity) async {
        emit(state.copyWith(status: SignUpStatus.success));
      },
    );
  }

  void _onPasswordVisibilityToggled(
    SignUpPasswordVisibilityToggled event,
    Emitter<SignUpState> emit,
  ) {
    emit(state.copyWith(isPasswordVisible: !state.isPasswordVisible));
  }

  void _onConfirmPasswordVisibilityToggled(
    SignUpConfirmPasswordVisibilityToggled event,
    Emitter<SignUpState> emit,
  ) {
    emit(
      state.copyWith(isConfirmPasswordVisible: !state.isConfirmPasswordVisible),
    );
  }

  // TODO: implement better validation
  bool _isValidEmail(String email) {
    return RegExp(r'^[^@]+@[^@]+\.[^@]+').hasMatch(email);
  }
}

[{"type": {"import": "package:conduit/features/auth/presentation/bloc/auth/auth_bloc.dart", "name": "AuthBloc", "isNullable": false, "isRecordType": false, "nameInRecord": null}, "typeImpl": {"import": "package:conduit/features/auth/presentation/bloc/auth/auth_bloc.dart", "name": "AuthBloc", "isNullable": false, "isRecordType": false, "nameInRecord": null}, "isAsync": false, "postConstructReturnsSelf": false, "preResolve": false, "canBeConst": false, "injectableType": 2, "dependsOn": [], "environments": [], "dependencies": [{"type": {"import": "package:conduit/features/auth/domain/use_cases/check_auth_use_case.dart", "name": "CheckAuthUseCase", "isNullable": false, "isRecordType": false, "nameInRecord": null}, "instanceName": null, "paramName": "_checkAuthUseCase", "isFactoryParam": false, "isPositional": true}, {"type": {"import": "package:conduit/features/auth/domain/use_cases/refresh_api_token_use_case.dart", "name": "RefreshApiTokenUseCase", "isNullable": false, "isRecordType": false, "nameInRecord": null}, "instanceName": null, "paramName": "_refreshApiTokenUseCase", "isFactoryParam": false, "isPositional": true}, {"type": {"import": "package:conduit/features/auth/domain/use_cases/sign_out_use_case.dart", "name": "SignOutUseCase", "isNullable": false, "isRecordType": false, "nameInRecord": null}, "instanceName": null, "paramName": "_signOutUseCase", "isFactoryParam": false, "isPositional": true}], "orderPosition": 0}]
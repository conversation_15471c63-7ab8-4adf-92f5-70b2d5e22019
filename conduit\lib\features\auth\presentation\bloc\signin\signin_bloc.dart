import 'package:conduit/core/use_case/use_case.dart';
import 'package:conduit/features/auth/domain/use_cases/sign_in_with_apple_use_case.dart';
import 'package:conduit/features/auth/domain/use_cases/sign_in_with_email_and_password_use_case.dart';
import 'package:conduit/features/auth/domain/use_cases/sign_in_with_google_use_case.dart';
import 'package:injectable/injectable.dart';

import 'signin_event.dart';
import 'signin_state.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

@injectable
class SignInBloc extends Bloc<SignInEvent, SignInState> {
  final SignInWithAppleUseCase _signInWithAppleUseCase;
  final SignInWithGoogleUseCase _signInWithGoogleUseCase;
  final SignInWithEmailAndPasswordUseCase _signInWithEmailAndPasswordUseCase;

  SignInBloc(
    this._signInWithAppleUseCase,
    this._signInWithGoogleUseCase,
    this._signInWithEmailAndPasswordUseCase,
  ) : super(const SignInState()) {
    on<SignInEmailChanged>(_onEmailChanged);
    on<SignInPasswordChanged>(_onPasswordChanged);
    on<SignInSubmitted>(_onSubmitted);
    on<SignInWithGoogle>(_onSignInWithGoogle);
    on<SignInWithApple>(_onSignInWithApple);
    on<SignInPasswordVisibilityToggled>(_onPasswordVisibilityToggled);
  }

  void _onEmailChanged(SignInEmailChanged event, Emitter<SignInState> emit) {
    final isEmailValid = _isValidEmail(event.email);
    emit(
      state.copyWith(
        email: event.email,
        isEmailValid: isEmailValid,
        status: SignInStatus.initial,
      ),
    );
  }

  void _onPasswordChanged(
    SignInPasswordChanged event,
    Emitter<SignInState> emit,
  ) {
    final isPasswordValid = event.password.length >= 6;
    emit(
      state.copyWith(
        password: event.password,
        isPasswordValid: isPasswordValid,
        status: SignInStatus.initial,
      ),
    );
  }

  Future<void> _onSubmitted(
    SignInSubmitted event,
    Emitter<SignInState> emit,
  ) async {
    if (!state.isEmailValid || !state.isPasswordValid) return;

    emit(state.copyWith(status: SignInStatus.loading));

    // First authenticate with Firebase
    final result = await _signInWithEmailAndPasswordUseCase.call(
      Params(email: state.email, password: state.password),
    );

    result.fold(
      (failure) => emit(state.copyWith(status: SignInStatus.failure)),
      (userEntity) async {
        emit(state.copyWith(status: SignInStatus.success));
      },
    );
  }

  Future<void> _onSignInWithGoogle(
    SignInWithGoogle event,
    Emitter<SignInState> emit,
  ) async {
    emit(state.copyWith(status: SignInStatus.loading));

    final result = await _signInWithGoogleUseCase.call(NoParams());

    result.fold(
      (failure) => emit(state.copyWith(status: SignInStatus.failure)),
      (userEntity) async {
        emit(state.copyWith(status: SignInStatus.success));
      },
    );
  }

  Future<void> _onSignInWithApple(
    SignInWithApple event,
    Emitter<SignInState> emit,
  ) async {
    emit(state.copyWith(status: SignInStatus.loading));

    final result = await _signInWithAppleUseCase.call(NoParams());

    result.fold(
      (failure) => emit(state.copyWith(status: SignInStatus.failure)),
      (userEntity) async {
        emit(state.copyWith(status: SignInStatus.success));
      },
    );
  }

  void _onPasswordVisibilityToggled(
    SignInPasswordVisibilityToggled event,
    Emitter<SignInState> emit,
  ) {
    emit(state.copyWith(isPasswordVisible: !state.isPasswordVisible));
  }

  // TODO: implement better validation
  bool _isValidEmail(String email) {
    return RegExp(r'^[^@]+@[^@]+\.[^@]+').hasMatch(email);
  }
}

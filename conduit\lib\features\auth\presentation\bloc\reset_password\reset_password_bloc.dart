import 'package:conduit/features/auth/domain/use_cases/reset_password_use_case.dart';
import 'package:injectable/injectable.dart';
import 'reset_password_state.dart';
import 'reset_password_event.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

@injectable
class ResetPasswordBloc extends Bloc<ResetPasswordEvent, ResetPasswordState> {
  final ResetPasswordUseCase _resetPasswordUseCase;

  ResetPasswordBloc({required ResetPasswordUseCase resetPasswordUseCase})
    : _resetPasswordUseCase = resetPasswordUseCase,
      super(const ResetPasswordState()) {
    on<ResetPasswordEmailChanged>(_onEmailChanged);
    on<ResetPasswordSubmitted>(_onSubmitted);
  }

  void _onEmailChanged(
    ResetPasswordEmailChanged event,
    Emitter<ResetPasswordState> emit,
  ) {
    final isEmailValid = _isValidEmail(event.email);
    emit(
      state.copyWith(
        email: event.email,
        isEmailValid: isEmailValid,
        status: ResetPasswordStatus.initial,
      ),
    );
  }

  Future<void> _onSubmitted(
    ResetPasswordSubmitted event,
    Emitter<ResetPasswordState> emit,
  ) async {
    if (!state.isEmailValid) return;

    emit(state.copyWith(status: ResetPasswordStatus.loading));

    final result = await _resetPasswordUseCase.call(Params(email: state.email));

    result.fold(
      (failure) => emit(state.copyWith(status: ResetPasswordStatus.failure)),
      (r) => emit(state.copyWith(status: ResetPasswordStatus.success)),
    );
  }

  // TODO: implement better validation
  bool _isValidEmail(String email) {
    return RegExp(r'^[^@]+@[^@]+\.[^@]+').hasMatch(email);
  }
}

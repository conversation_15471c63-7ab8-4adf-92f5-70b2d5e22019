import 'package:conduit/core/di/service_locator.dart';
import 'package:conduit/core/presentation/pages/not_found_error_page.dart';
import 'package:conduit/core/router/app_shell.dart';
import 'package:conduit/features/auth/presentation/bloc/auth/auth_bloc.dart';
import 'package:conduit/features/auth/presentation/pages/signin_page.dart';
import 'package:conduit/features/auth/presentation/pages/signup_page.dart';
import 'package:conduit/features/dashboard/presentation/pages/dashboard_page.dart';
import 'package:conduit/features/profile/presentation/pages/profile_page.dart';
import 'package:conduit/features/profile/presentation/pages/settings_page.dart';
import 'package:conduit/core/presentation/pages/splash_page.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'route_names.dart';

class AppRouter {
  static final _rootNavigatorKey = GlobalKey<NavigatorState>();
  static final _shellNavigatorKey = GlobalKey<NavigatorState>();

  static GoRouter router = GoRouter(
    navigatorKey: _rootNavigatorKey,
    initialLocation: RouteNames.splash,
    debugLogDiagnostics: true,
    redirect: _redirect,
    refreshListenable: getIt<AuthBloc>(),
    routes: [
      GoRoute(
        path: RouteNames.splash,
        name: 'splash',
        builder: (context, state) => const SplashPage(),
      ),
      // Authentication routes (no shell)
      GoRoute(
        path: RouteNames.signIn,
        name: 'signin',
        builder: (context, state) => const SignInPage(),
      ),
      GoRoute(
        path: RouteNames.singUp,
        name: 'signup',
        builder: (context, state) => const SignUpPage(),
      ),

      // Main app shell with authenticated routes
      ShellRoute(
        navigatorKey: _shellNavigatorKey,
        builder: (context, state, child) {
          return AppShell(child: child);
        },
        routes: [
          GoRoute(
            path: RouteNames.dashboard,
            name: 'dashboard',
            builder: (context, state) => const DashboardPage(),
          ),
          GoRoute(
            path: RouteNames.profile,
            name: 'profile',
            builder: (context, state) => const ProfilePage(),
            // routes: [
            //   GoRoute(
            //     path: 'edit',
            //     name: 'edit-profile',
            //     builder: (context, state) => const EditProfilePage(),
            //   ),
            // ],
          ),
          GoRoute(
            path: RouteNames.settings,
            name: 'settings',
            builder: (context, state) => const SettingsPage(),
            // routes: [
            //   GoRoute(
            //     path: 'notifications',
            //     name: 'notifications-settings',
            //     builder: (context, state) => const NotificationSettingsPage(),
            //   ),
            // ],
          ),
        ],
      ),
    ],
    errorBuilder: (context, state) => NotFoundErrorPage(),
  );

  static String? _redirect(BuildContext context, GoRouterState state) {
    final authBloc = getIt<AuthBloc>();
    final isAuthenticated = authBloc.isAuthenticated;
    final isLoading = authBloc.isLoading;

    // Show splash while checking auth status
    if (isLoading && state.uri.path != RouteNames.splash) {
      return RouteNames.splash;
    }

    final isOnAuthRoute =
        state.uri.path == RouteNames.signIn ||
        state.uri.path == RouteNames.singUp;
    final isOnSplash = state.uri.path == RouteNames.splash;

    // If auth is loaded and we're on splash, redirect based on auth status
    if (!isLoading && isOnSplash) {
      return isAuthenticated ? RouteNames.dashboard : RouteNames.signIn;
    }

    // Redirect to login if not authenticated and not already on auth route
    if (!isLoading && !isAuthenticated && !isOnAuthRoute && !isOnSplash) {
      return RouteNames.signIn;
    }

    // Redirect to dashboard if authenticated and on auth route
    if (!isLoading && isAuthenticated && isOnAuthRoute) {
      return RouteNames.dashboard;
    }

    return null;
  }
}

import 'package:conduit/core/errors/failures.dart';
import 'package:conduit/core/use_case/use_case.dart';
import 'package:conduit/features/auth/domain/entities/auth_token_entity.dart';
import 'package:conduit/features/auth/domain/repositories/auth_repository.dart';
import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import 'package:injectable/injectable.dart';

@injectable
class RefreshApiTokenUseCase implements UseCase<AuthTokenEntity, Params> {
  final AuthRepository authRepository;

  RefreshApiTokenUseCase(this.authRepository);

  @override
  Future<Either<Failure, AuthTokenEntity>> call(Params params) async {
    return await authRepository.refreshApiToken();
  }
}

class Params extends Equatable {
  final String token;

  const Params(this.token);

  @override
  List<Object> get props => [token];
}

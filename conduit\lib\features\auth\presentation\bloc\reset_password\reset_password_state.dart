import 'package:equatable/equatable.dart';

enum ResetPasswordStatus { initial, loading, success, failure }

class ResetPasswordState extends Equatable {
  final ResetPasswordStatus status;
  final String email;
  final String? errorMessage;
  final bool isEmailValid;

  const ResetPasswordState({
    this.status = ResetPasswordStatus.initial,
    this.email = '',
    this.errorMessage,
    this.isEmailValid = false,
  });

  ResetPasswordState copyWith({
    ResetPasswordStatus? status,
    String? email,
    String? errorMessage,
    bool? isEmailValid,
  }) {
    return ResetPasswordState(
      status: status ?? this.status,
      email: email ?? this.email,
      errorMessage: errorMessage ?? this.errorMessage,
      isEmailValid: isEmailValid ?? this.isEmailValid,
    );
  }

  @override
  List<Object?> get props => [status, email, errorMessage, isEmailValid];
}

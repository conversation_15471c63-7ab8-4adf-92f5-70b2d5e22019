import 'package:conduit/core/errors/failures.dart';
import 'package:conduit/features/auth/data/models/auth_token_model.dart';
import 'package:conduit/features/auth/domain/entities/auth_token_entity.dart';
import 'package:conduit/features/auth/data/models/user_model.dart';
import 'package:dartz/dartz.dart';
import 'package:dio/dio.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:injectable/injectable.dart';

// Helper class to return both user and tokens from a single remote call
class AuthResult {
  final UserModel user;
  final AuthTokenEntity tokens;
  AuthResult({required this.user, required this.tokens});
}

abstract class AuthRemoteDataSource {
  Future<Either<Failure, AuthResult>> signInWithEmailAndPassword({
    required String email,
    required String password,
  });

  Future<Either<Failure, AuthResult>> signUpWithEmailAndPassword({
    required String email,
    required String password,
    required String name,
  });

  Future<Either<Failure, AuthResult>> authenticateWithThirdParty({
    required String providerToken, // e.g., from Google, Apple
    required String provider, // 'google', 'apple'
  });

  Future<Either<Failure, void>> signOut();
  Future<Either<Failure, void>> resetPassword({required String email});
}

@LazySingleton(as: AuthRemoteDataSource)
class AuthRemoteDataSourceImpl implements AuthRemoteDataSource {
  final FirebaseAuth _firebaseAuth;
  final Dio _backendApiDio;

  AuthRemoteDataSourceImpl(
    this._firebaseAuth,
    @Named('backendApiDio') this._backendApiDio,
  );

  // Helper to centralize the two-step auth logic
  Future<Either<Failure, AuthResult>> _authenticateAndVerifyWithApi(
    Future<UserCredential> Function() authCall,
  ) async {
    try {
      // Step 1: Authenticate with Firebase
      final userCredential = await authCall();
      final firebaseToken = await userCredential.user?.getIdToken();

      if (firebaseToken == null) {
        return Left(
          ServerFailure(message: 'Could not retrieve Firebase token.'),
        );
      }

      // Step 2: Send Firebase token to your backend API for verification
      final response = await _backendApiDio.post(
        '/auth/verify-firebase', // Example endpoint
        data: {'token': firebaseToken},
      );

      // Assumes your API responds with its own tokens and the user profile
      final user = UserModel.fromJson(response.data['user']);
      final apiTokens = AuthTokenModel.fromJson(response.data['tokens']);

      return Right(AuthResult(user: user, tokens: apiTokens));
    } on FirebaseAuthException catch (e) {
      return Left(
        ServerFailure(message: e.message ?? 'Firebase authentication failed.'),
      );
    } on DioException catch (e) {
      final message =
          e.response?.data?['message'] ?? 'API authentication failed.';
      return Left(ServerFailure(message: message));
    } catch (e) {
      return Left(ServerFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, AuthResult>> signInWithEmailAndPassword({
    required String email,
    required String password,
  }) {
    return _authenticateAndVerifyWithApi(
      () => _firebaseAuth.signInWithEmailAndPassword(
        email: email,
        password: password,
      ),
    );
  }

  @override
  Future<Either<Failure, AuthResult>> signUpWithEmailAndPassword({
    required String email,
    required String password,
    required String name,
  }) {
    // Note: You would likely send the 'name' to your API after Firebase auth
    // This is a simplification. A real implementation might require a different endpoint.
    return _authenticateAndVerifyWithApi(
      () => _firebaseAuth.createUserWithEmailAndPassword(
        email: email,
        password: password,
      ),
    );
  }

  @override
  Future<Either<Failure, void>> signOut() async {
    try {
      // Sign out from Firebase first.
      await _firebaseAuth.signOut();

      // Optional: Notify your backend. We can ignore failures here
      // as the main goal is to clear the local session.
      try {
        await _backendApiDio.post('/auth/signout');
      } catch (_) {}

      return const Right(null);
    } on FirebaseAuthException catch (e) {
      return Left(ServerFailure(message: e.message ?? 'Sign out failed.'));
    }
  }

  @override
  Future<Either<Failure, void>> resetPassword({required String email}) async {
    try {
      await _firebaseAuth.sendPasswordResetEmail(email: email);
      return const Right(null);
    } on FirebaseAuthException catch (e) {
      return Left(
        ServerFailure(message: e.message ?? 'Password reset failed.'),
      );
    }
  }

  // Not implemented here as it requires a specific provider token flow.
  @override
  Future<Either<Failure, AuthResult>> authenticateWithThirdParty({
    required String providerToken,
    required String provider,
  }) {
    // This would call an endpoint like '/auth/verify-google' or '/auth/verify-apple'
    // and would not use the `_authenticateAndVerifyWithApi` helper directly.
    throw UnimplementedError();
  }
}

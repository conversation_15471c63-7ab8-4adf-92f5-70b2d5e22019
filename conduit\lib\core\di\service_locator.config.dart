// dart format width=80
// GENERATED CODE - DO NOT MODIFY BY HAND

// **************************************************************************
// InjectableConfigGenerator
// **************************************************************************

// ignore_for_file: type=lint
// coverage:ignore-file

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'package:dio/dio.dart' as _i361;
import 'package:firebase_auth/firebase_auth.dart' as _i59;
import 'package:flutter_secure_storage/flutter_secure_storage.dart' as _i558;
import 'package:get_it/get_it.dart' as _i174;
import 'package:injectable/injectable.dart' as _i526;

import '../../features/auth/data/data_sources/auth_local_data_source.dart'
    as _i606;
import '../../features/auth/data/data_sources/auth_remote_data_source.dart'
    as _i25;
import '../../features/auth/data/repositories/auth_repository_impl.dart'
    as _i153;
import '../../features/auth/domain/repositories/auth_repository.dart' as _i787;
import '../../features/auth/domain/use_cases/check_auth_use_case.dart' as _i269;
import '../../features/auth/domain/use_cases/refresh_api_token_use_case.dart'
    as _i1047;
import '../../features/auth/domain/use_cases/reset_password_use_case.dart'
    as _i169;
import '../../features/auth/domain/use_cases/sign_in_with_apple_use_case.dart'
    as _i402;
import '../../features/auth/domain/use_cases/sign_in_with_email_and_password_use_case.dart'
    as _i98;
import '../../features/auth/domain/use_cases/sign_in_with_google_use_case.dart'
    as _i247;
import '../../features/auth/domain/use_cases/sign_out_use_case.dart' as _i131;
import '../../features/auth/domain/use_cases/sign_up_with_email_and_password_use_case.dart'
    as _i757;
import '../../features/auth/presentation/bloc/auth/auth_bloc.dart' as _i469;
import '../../features/auth/presentation/bloc/reset_password/reset_password_bloc.dart'
    as _i591;
import '../../features/auth/presentation/bloc/signin/signin_bloc.dart' as _i999;
import '../../features/auth/presentation/bloc/signup/signup_bloc.dart' as _i173;
import '../network/api_clients.dart' as _i953;
import '../network/api_interceptors.dart' as _i846;
import 'service_locator.dart' as _i105;

extension GetItInjectableX on _i174.GetIt {
  // initializes the registration of main-scope dependencies inside of GetIt
  _i174.GetIt init({
    String? environment,
    _i526.EnvironmentFilter? environmentFilter,
  }) {
    final gh = _i526.GetItHelper(this, environment, environmentFilter);
    final registerModule = _$RegisterModule();
    final networkModule = _$NetworkModule();
    gh.lazySingleton<_i59.FirebaseAuth>(() => registerModule.firebaseAuth);
    gh.lazySingleton<_i558.FlutterSecureStorage>(
      () => registerModule.flutterSecureStorage,
    );
    gh.lazySingleton<_i606.AuthLocalDataSource>(
      () => _i606.AuthLocalDataSourceImpl(gh<_i558.FlutterSecureStorage>()),
    );
    gh.factory<_i846.AuthInterceptor>(
      () => _i846.AuthInterceptor(
        gh<_i846.TokenProvider>(),
        gh<_i846.TokenRefresher>(),
      ),
    );
    gh.lazySingleton<_i361.Dio>(
      () => networkModule.backendApiDio(gh<_i846.AuthInterceptor>()),
      instanceName: 'backendApiDio',
    );
    gh.lazySingleton<_i25.AuthRemoteDataSource>(
      () => _i25.AuthRemoteDataSourceImpl(
        gh<_i59.FirebaseAuth>(),
        gh<_i361.Dio>(instanceName: 'backendApiDio'),
      ),
    );
    gh.lazySingleton<_i953.BackendApiClient>(
      () => _i953.BackendApiClientImpl(
        gh<_i361.Dio>(instanceName: 'backendApiDio'),
      ),
      instanceName: 'backendApiClient',
    );
    gh.lazySingleton<_i787.AuthRepository>(
      () => _i153.AuthRepositoryImpl(
        gh<_i25.AuthRemoteDataSource>(),
        gh<_i606.AuthLocalDataSource>(),
      ),
    );
    gh.factory<_i269.CheckAuthUseCase>(
      () => _i269.CheckAuthUseCase(gh<_i787.AuthRepository>()),
    );
    gh.factory<_i1047.RefreshApiTokenUseCase>(
      () => _i1047.RefreshApiTokenUseCase(gh<_i787.AuthRepository>()),
    );
    gh.factory<_i169.ResetPasswordUseCase>(
      () => _i169.ResetPasswordUseCase(gh<_i787.AuthRepository>()),
    );
    gh.factory<_i402.SignInWithAppleUseCase>(
      () => _i402.SignInWithAppleUseCase(gh<_i787.AuthRepository>()),
    );
    gh.factory<_i98.SignInWithEmailAndPasswordUseCase>(
      () => _i98.SignInWithEmailAndPasswordUseCase(gh<_i787.AuthRepository>()),
    );
    gh.factory<_i247.SignInWithGoogleUseCase>(
      () => _i247.SignInWithGoogleUseCase(gh<_i787.AuthRepository>()),
    );
    gh.factory<_i131.SignOutUseCase>(
      () => _i131.SignOutUseCase(gh<_i787.AuthRepository>()),
    );
    gh.factory<_i757.SignUpWithEmailAndPasswordUseCase>(
      () => _i757.SignUpWithEmailAndPasswordUseCase(gh<_i787.AuthRepository>()),
    );
    gh.factory<_i591.ResetPasswordBloc>(
      () => _i591.ResetPasswordBloc(
        resetPasswordUseCase: gh<_i169.ResetPasswordUseCase>(),
      ),
    );
    gh.lazySingleton<_i469.AuthBloc>(
      () => _i469.AuthBloc(
        gh<_i269.CheckAuthUseCase>(),
        gh<_i1047.RefreshApiTokenUseCase>(),
        gh<_i131.SignOutUseCase>(),
      ),
    );
    gh.factory<_i173.SignUpBloc>(
      () => _i173.SignUpBloc(
        signUpWithEmailAndPasswordUseCase:
            gh<_i757.SignUpWithEmailAndPasswordUseCase>(),
        signInWithAppleUseCase: gh<_i402.SignInWithAppleUseCase>(),
        signInWithGoogleUseCase: gh<_i247.SignInWithGoogleUseCase>(),
      ),
    );
    gh.factory<_i999.SignInBloc>(
      () => _i999.SignInBloc(
        gh<_i402.SignInWithAppleUseCase>(),
        gh<_i247.SignInWithGoogleUseCase>(),
        gh<_i98.SignInWithEmailAndPasswordUseCase>(),
      ),
    );
    return this;
  }
}

class _$RegisterModule extends _i105.RegisterModule {}

class _$NetworkModule extends _i105.NetworkModule {}

import 'package:conduit/core/errors/failures.dart';
import 'package:conduit/core/use_case/use_case.dart';
import 'package:conduit/features/auth/domain/repositories/auth_repository.dart';
import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import 'package:injectable/injectable.dart';

@injectable
class ResetPasswordUseCase implements UseCase<void, Params> {
  final AuthRepository authRepository;

  ResetPasswordUseCase(this.authRepository);

  @override
  Future<Either<Failure, void>> call(Params params) async {
    return await authRepository.resetPassword(email: params.email);
  }
}

class Params extends Equatable {
  final String email;

  const Params({required this.email});

  @override
  List<Object> get props => [email];
}

import 'package:conduit/core/use_case/use_case.dart';
import 'package:conduit/features/auth/domain/use_cases/check_auth_use_case.dart';
import 'package:conduit/features/auth/domain/use_cases/refresh_api_token_use_case.dart';
import 'package:conduit/features/auth/domain/use_cases/sign_out_use_case.dart';
import 'package:injectable/injectable.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter/foundation.dart';

import 'auth_event.dart';
import 'auth_state.dart';

@lazySingleton
class AuthBloc extends Bloc<AuthEvent, AuthState> with ChangeNotifier {
  final CheckAuthUseCase _checkAuthUseCase;
  final RefreshApiTokenUseCase _refreshApiTokenUseCase;
  final SignOutUseCase _signOutUseCase;

  AuthBloc(
    this._checkAuthUseCase,
    this._refreshApiTokenUseCase,
    this._signOutUseCase,
  ) : super(AuthInitial()) {
    on<AuthCheckRequested>(_onAuthCheckRequested);
    on<AuthSignOutRequested>(_onSignOutRequested);
    on<AuthTokenRefreshRequested>(_onTokenRefreshRequested);

    // Check auth status on initialization
    add(AuthCheckRequested());
  }

  // Convenience getters for router
  bool get isAuthenticated => state is AuthAuthenticated;
  bool get isLoading => state is AuthLoading || state is AuthInitial;
  String? get accessToken => state is AuthAuthenticated
      ? (state as AuthAuthenticated).accessToken
      : null;
  String? get userId =>
      state is AuthAuthenticated ? (state as AuthAuthenticated).userId : null;

  @override
  void emit(AuthState state) {
    // super.emit(state);
    notifyListeners(); // Notify GoRouter of state changes
  }

  Future<void> _onAuthCheckRequested(
    AuthCheckRequested event,
    Emitter<AuthState> emit,
  ) async {
    emit(AuthLoading());

    final result = await _checkAuthUseCase.call(NoParams());

    result.fold(
      (failure) => emit(AuthError(message: failure.message)),
      (isAuthenticated) => emit(
        isAuthenticated
            ? AuthAuthenticated(userId: '', accessToken: '')
            : AuthUnauthenticated(),
      ),
    );
  }

  Future<void> _onSignOutRequested(
    AuthSignOutRequested event,
    Emitter<AuthState> emit,
  ) async {
    emit(AuthLoading());

    final result = await _signOutUseCase.call(NoParams());

    result.fold(
      (failure) => emit(AuthError(message: failure.message)),
      (r) => emit(AuthUnauthenticated()),
    );
  }

  Future<void> _onTokenRefreshRequested(
    AuthTokenRefreshRequested event,
    Emitter<AuthState> emit,
  ) async {
    if (state is! AuthAuthenticated) return;

    final currentState = state as AuthAuthenticated;

    final result = await _refreshApiTokenUseCase.call(
      Params(currentState.accessToken!),
    );
    result.fold(
      (failure) {
        emit(AuthError(message: failure.message));
        emit(AuthUnauthenticated());
      },
      (tokenEntity) => emit(
        AuthAuthenticated(
          userId: currentState.userId,
          accessToken: tokenEntity.accessToken,
        ),
      ),
    );
  }
}

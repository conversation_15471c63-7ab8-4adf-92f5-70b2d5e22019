import 'package:conduit/core/di/service_locator.dart';
import 'package:conduit/core/presentation/extensions/context_extensions.dart';
import 'package:conduit/core/router/route_names.dart';
import 'package:conduit/features/auth/presentation/bloc/signin/signin_bloc.dart';
import 'package:conduit/features/auth/presentation/bloc/signin/signin_event.dart';
import 'package:conduit/features/auth/presentation/bloc/signin/signin_state.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class SignInPage extends StatelessWidget {
  const SignInPage({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) =>
          getIt<SignInBloc>(), // Use GetIt instead of context.read
      child: const SignInView(),
    );
  }
}

class SignInView extends StatelessWidget {
  const SignInView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Theme.of(context).colorScheme.surface,
      body: BlocListener<SignInBloc, SignInState>(
        listener: (context, state) {
          if (state.status == SignInStatus.failure) {
            context.showErrorSnackBar(
              message: state.errorMessage ?? 'Sign in failed',
            );
          }
          if (state.status == SignInStatus.success) {
            context.showSuccessSnackBar(message: 'Welcome!');
            Navigator.of(
              context,
            ).pushNamedAndRemoveUntil(RouteNames.dashboard, (route) => false);
          }
        },
        child: SafeArea(
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(24.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                const SizedBox(height: 60),
                _buildHeader(context),
                const SizedBox(height: 48),
                _buildSignInForm(context),
                const SizedBox(height: 32),
                _buildSocialSignIn(context),
                const SizedBox(height: 24),
                _buildBottomLinks(context),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Column(
      children: [
        Icon(
          Icons.lock_outline,
          size: 80,
          color: Theme.of(context).colorScheme.primary,
        ),
        const SizedBox(height: 24),
        Text(
          'Welcome Back',
          style: Theme.of(context).textTheme.headlineMedium?.copyWith(
            fontWeight: FontWeight.bold,
            color: Theme.of(context).colorScheme.onSurface,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          'Sign in to your account',
          style: Theme.of(context).textTheme.bodyLarge?.copyWith(
            color: Theme.of(context).colorScheme.onSurfaceVariant,
          ),
        ),
      ],
    );
  }

  Widget _buildSignInForm(BuildContext context) {
    return BlocBuilder<SignInBloc, SignInState>(
      builder: (context, state) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            _buildEmailField(context, state),
            const SizedBox(height: 20),
            _buildPasswordField(context, state),
            const SizedBox(height: 12),
            _buildForgotPasswordLink(context),
            const SizedBox(height: 32),
            _buildSignInButton(context, state),
          ],
        );
      },
    );
  }

  Widget _buildEmailField(BuildContext context, SignInState state) {
    return TextFormField(
      onChanged: (email) =>
          context.read<SignInBloc>().add(SignInEmailChanged(email)),
      keyboardType: TextInputType.emailAddress,
      textInputAction: TextInputAction.next,
      style: const TextStyle(fontSize: 16),
      decoration: InputDecoration(
        labelText: 'Email',
        prefixIcon: const Icon(Icons.email_outlined, size: 24),
        border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
        contentPadding: const EdgeInsets.symmetric(
          horizontal: 16,
          vertical: 20,
        ),
        errorText: state.email.isNotEmpty && !state.isEmailValid
            ? 'Please enter a valid email'
            : null,
      ),
    );
  }

  Widget _buildPasswordField(BuildContext context, SignInState state) {
    return TextFormField(
      onChanged: (password) =>
          context.read<SignInBloc>().add(SignInPasswordChanged(password)),
      obscureText: !state.isPasswordVisible,
      textInputAction: TextInputAction.done,
      style: const TextStyle(fontSize: 16),
      decoration: InputDecoration(
        labelText: 'Password',
        prefixIcon: const Icon(Icons.lock_outlined, size: 24),
        suffixIcon: IconButton(
          icon: Icon(
            state.isPasswordVisible ? Icons.visibility_off : Icons.visibility,
            size: 24,
          ),
          onPressed: () =>
              context.read<SignInBloc>().add(SignInPasswordVisibilityToggled()),
        ),
        border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
        contentPadding: const EdgeInsets.symmetric(
          horizontal: 16,
          vertical: 20,
        ),
        errorText: state.password.isNotEmpty && !state.isPasswordValid
            ? 'Password must be at least 6 characters'
            : null,
      ),
    );
  }

  Widget _buildForgotPasswordLink(BuildContext context) {
    return Align(
      alignment: Alignment.centerRight,
      child: TextButton(
        onPressed: () => Navigator.pushNamed(context, '/reset-password'),
        child: Text(
          'Forgot Password?',
          style: TextStyle(
            color: Theme.of(context).colorScheme.primary,
            fontSize: 14,
            fontWeight: FontWeight.w500,
          ),
        ),
      ),
    );
  }

  Widget _buildSignInButton(BuildContext context, SignInState state) {
    final isLoading = state.status == SignInStatus.loading;
    final canSubmit = state.isEmailValid && state.isPasswordValid && !isLoading;

    return ElevatedButton(
      onPressed: canSubmit
          ? () => context.read<SignInBloc>().add(SignInSubmitted())
          : null,
      style: ElevatedButton.styleFrom(
        padding: const EdgeInsets.symmetric(vertical: 18),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        textStyle: const TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
      ),
      child: isLoading
          ? const SizedBox(
              height: 20,
              width: 20,
              child: CircularProgressIndicator(strokeWidth: 2),
            )
          : const Text('Sign In'),
    );
  }

  Widget _buildSocialSignIn(BuildContext context) {
    return BlocBuilder<SignInBloc, SignInState>(
      builder: (context, state) {
        final isLoading = state.status == SignInStatus.loading;

        return Column(
          children: [
            Row(
              children: [
                Expanded(
                  child: Divider(color: Theme.of(context).colorScheme.outline),
                ),
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  child: Text(
                    'Or continue with',
                    style: TextStyle(
                      color: Theme.of(context).colorScheme.onSurfaceVariant,
                      fontSize: 14,
                    ),
                  ),
                ),
                Expanded(
                  child: Divider(color: Theme.of(context).colorScheme.outline),
                ),
              ],
            ),
            const SizedBox(height: 24),
            Row(
              children: [
                Expanded(
                  child: _buildSocialButton(
                    context: context,
                    icon: Icons.g_mobiledata,
                    label: 'Google',
                    onPressed: isLoading
                        ? null
                        : () => context.read<SignInBloc>().add(
                            SignInWithGoogle(),
                          ),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: _buildSocialButton(
                    context: context,
                    icon: Icons.apple,
                    label: 'Apple',
                    onPressed: isLoading
                        ? null
                        : () =>
                              context.read<SignInBloc>().add(SignInWithApple()),
                  ),
                ),
              ],
            ),
          ],
        );
      },
    );
  }

  Widget _buildSocialButton({
    required BuildContext context,
    required IconData icon,
    required String label,
    required VoidCallback? onPressed,
  }) {
    return OutlinedButton.icon(
      onPressed: onPressed,
      icon: Icon(icon, size: 24),
      label: Text(label),
      style: OutlinedButton.styleFrom(
        padding: const EdgeInsets.symmetric(vertical: 16),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        textStyle: const TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
      ),
    );
  }

  Widget _buildBottomLinks(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Text(
          "Don't have an account? ",
          style: TextStyle(
            color: Theme.of(context).colorScheme.onSurfaceVariant,
            fontSize: 14,
          ),
        ),
        TextButton(
          onPressed: () => Navigator.pushNamed(context, '/signup'),
          child: Text(
            'Sign Up',
            style: TextStyle(
              color: Theme.of(context).colorScheme.primary,
              fontSize: 14,
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
      ],
    );
  }
}

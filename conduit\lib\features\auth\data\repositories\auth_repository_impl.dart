import 'package:conduit/core/errors/failures.dart';
import 'package:conduit/features/auth/data/data_sources/auth_local_data_source.dart';
import 'package:conduit/features/auth/data/data_sources/auth_remote_data_source.dart';
import 'package:conduit/features/auth/domain/entities/auth_token_entity.dart';
import 'package:conduit/features/auth/domain/entities/user_entity.dart';
import 'package:conduit/features/auth/domain/repositories/auth_repository.dart';
import 'package:dartz/dartz.dart';
import 'package:injectable/injectable.dart';

@LazySingleton(as: AuthRepository)
class AuthRepositoryImpl implements AuthRepository {
  final AuthRemoteDataSource _remoteDataSource;
  final AuthLocalDataSource _localDataSource;

  AuthRepositoryImpl(this._remoteDataSource, this._localDataSource);

  Future<Either<Failure, UserEntity>> _handleAuthFlow(
    Future<Either<Failure, AuthResult>> Function() remoteCall,
  ) async {
    final remoteResult = await remoteCall();

    return remoteResult.fold(
      (failure) => Left(failure), // Remote call failed, return the failure
      (authResult) async {
        // Remote call succeeded, now store the tokens locally
        final storeResult = await _localDataSource.storeTokens(
          authResult.tokens,
        );
        return storeResult.fold(
          (failure) =>
              Left(failure), // Storing tokens failed, return the failure
          (_) => Right(authResult.user), // Success! Return the user
        );
      },
    );
  }

  @override
  Future<Either<Failure, UserEntity>> signInWithEmailAndPassword({
    required String email,
    required String password,
  }) {
    return _handleAuthFlow(
      () => _remoteDataSource.signInWithEmailAndPassword(
        email: email,
        password: password,
      ),
    );
  }

  @override
  Future<Either<Failure, UserEntity>> signUpWithEmailAndPassword({
    required String email,
    required String password,
    required String name,
  }) {
    return _handleAuthFlow(
      () => _remoteDataSource.signUpWithEmailAndPassword(
        email: email,
        password: password,
        name: name,
      ),
    );
  }

  @override
  Future<Either<Failure, void>> signOut() async {
    final remoteSignOutResult = await _remoteDataSource.signOut();
    // Always clear local data, even if remote sign-out has an issue.
    // The user must be able to sign out of the device.
    await _localDataSource.clearTokens();
    return remoteSignOutResult;
  }

  @override
  Future<Either<Failure, void>> resetPassword({required String email}) {
    return _remoteDataSource.resetPassword(email: email);
  }

  @override
  Future<Either<Failure, bool>> checkAuth() async {
    final tokenResult = await _localDataSource.getTokens();
    return tokenResult.fold(
      (failure) => Left(failure),
      (tokens) => Right(tokens != null), // True if tokens exist
    );
  }

  @override
  Future<Either<Failure, void>> clearAuthData() {
    return _localDataSource.clearTokens();
  }

  @override
  Future<Either<Failure, AuthTokenEntity?>> getStoredTokens() {
    return _localDataSource.getTokens();
  }

  // --- Methods that are placeholders or require more complex flows ---

  @override
  Future<Either<Failure, AuthTokenEntity>> authenticateWithApi({
    required String token,
  }) {
    // This method is now effectively handled by the remote data source internally.
    // If you need to expose it directly, you'd add it to the remote data source contract.
    throw UnimplementedError('This flow is now part of the sign-in process.');
  }

  @override
  Future<Either<Failure, AuthTokenEntity>> refreshApiToken() {
    // This would require a `refreshApiToken` method on both remote and local data sources.
    throw UnimplementedError();
  }

  @override
  Future<Either<Failure, UserEntity>> signInWithApple() {
    // The UI layer would get the token from the Apple SDK, then call a usecase
    // which in turn would call a new repository method like `signInWithThirdParty`.
    throw UnimplementedError('Requires UI layer integration first.');
  }

  @override
  Future<Either<Failure, UserEntity>> signInWithGoogle() {
    // The UI layer would get the token from the Google Sign-In SDK, then call a usecase
    // which in turn would call a new repository method like `signInWithThirdParty`.
    throw UnimplementedError('Requires UI layer integration first.');
  }

  @override
  Future<Either<Failure, void>> storeApiToken({
    required String accessToken,
    String? refreshToken,
    String?
    userId, // userId would now be part of the UserEntity, not stored separately
  }) {
    final tokens = AuthTokenEntity(
      accessToken: accessToken,
      refreshToken: refreshToken,
    );
    return _localDataSource.storeTokens(tokens);
  }
}

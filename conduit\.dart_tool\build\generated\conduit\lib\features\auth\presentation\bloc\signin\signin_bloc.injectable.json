[{"type": {"import": "package:conduit/features/auth/presentation/bloc/signin/signin_bloc.dart", "name": "SignInBloc", "isNullable": false, "isRecordType": false, "nameInRecord": null}, "typeImpl": {"import": "package:conduit/features/auth/presentation/bloc/signin/signin_bloc.dart", "name": "SignInBloc", "isNullable": false, "isRecordType": false, "nameInRecord": null}, "isAsync": false, "postConstructReturnsSelf": false, "preResolve": false, "canBeConst": false, "injectableType": 0, "dependsOn": [], "environments": [], "dependencies": [{"type": {"import": "package:conduit/features/auth/domain/use_cases/sign_in_with_apple_use_case.dart", "name": "SignInWithAppleUseCase", "isNullable": false, "isRecordType": false, "nameInRecord": null}, "instanceName": null, "paramName": "_signInWithAppleUseCase", "isFactoryParam": false, "isPositional": true}, {"type": {"import": "package:conduit/features/auth/domain/use_cases/sign_in_with_google_use_case.dart", "name": "SignInWithGoogleUseCase", "isNullable": false, "isRecordType": false, "nameInRecord": null}, "instanceName": null, "paramName": "_signInWithGoogleUseCase", "isFactoryParam": false, "isPositional": true}, {"type": {"import": "package:conduit/features/auth/domain/use_cases/sign_in_with_email_and_password_use_case.dart", "name": "SignInWithEmailAndPasswordUseCase", "isNullable": false, "isRecordType": false, "nameInRecord": null}, "instanceName": null, "paramName": "_signInWithEmailAndPasswordUseCase", "isFactoryParam": false, "isPositional": true}], "orderPosition": 0}]
import 'package:conduit/core/errors/failures.dart';
import 'package:conduit/core/use_case/use_case.dart';
import 'package:conduit/features/auth/domain/entities/user_entity.dart';
import 'package:conduit/features/auth/domain/repositories/auth_repository.dart';
import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import 'package:injectable/injectable.dart';

@injectable
class SignInWithEmailAndPasswordUseCase implements UseCase<UserEntity, Params> {
  final AuthRepository authRepository;

  SignInWithEmailAndPasswordUseCase(this.authRepository);

  @override
  Future<Either<Failure, UserEntity>> call(Params params) async {
    return await authRepository.signInWithEmailAndPassword(
      email: params.email,
      password: params.password,
    );
  }
}

class Params extends Equatable {
  final String email;
  final String password;

  const Params({required this.email, required this.password});

  @override
  List<Object> get props => [email, password];
}

[{"type": {"import": "package:conduit/features/auth/domain/repositories/auth_repository.dart", "name": "AuthRepository", "isNullable": false, "isRecordType": false, "nameInRecord": null}, "typeImpl": {"import": "package:conduit/features/auth/data/repositories/auth_repository_impl.dart", "name": "AuthRepositoryImpl", "isNullable": false, "isRecordType": false, "nameInRecord": null}, "isAsync": false, "postConstructReturnsSelf": false, "preResolve": false, "canBeConst": false, "injectableType": 2, "dependsOn": [], "environments": [], "dependencies": [{"type": {"import": "package:conduit/features/auth/data/data_sources/auth_remote_data_source.dart", "name": "AuthRemoteDataSource", "isNullable": false, "isRecordType": false, "nameInRecord": null}, "instanceName": null, "paramName": "_remoteDataSource", "isFactoryParam": false, "isPositional": true}, {"type": {"import": "package:conduit/features/auth/data/data_sources/auth_local_data_source.dart", "name": "AuthLocalDataSource", "isNullable": false, "isRecordType": false, "nameInRecord": null}, "instanceName": null, "paramName": "_localDataSource", "isFactoryParam": false, "isPositional": true}], "orderPosition": 0}]
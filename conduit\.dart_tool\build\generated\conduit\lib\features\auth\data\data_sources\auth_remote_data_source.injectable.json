[{"type": {"import": "package:conduit/features/auth/data/data_sources/auth_remote_data_source.dart", "name": "AuthRemoteDataSource", "isNullable": false, "isRecordType": false, "nameInRecord": null}, "typeImpl": {"import": "package:conduit/features/auth/data/data_sources/auth_remote_data_source.dart", "name": "AuthRemoteDataSourceImpl", "isNullable": false, "isRecordType": false, "nameInRecord": null}, "isAsync": false, "postConstructReturnsSelf": false, "preResolve": false, "canBeConst": false, "injectableType": 2, "dependsOn": [], "environments": [], "dependencies": [{"type": {"import": "package:firebase_auth/firebase_auth.dart", "name": "FirebaseAuth", "isNullable": false, "isRecordType": false, "nameInRecord": null}, "instanceName": null, "paramName": "_firebaseAuth", "isFactoryParam": false, "isPositional": true}, {"type": {"import": "package:dio/dio.dart", "name": "<PERSON><PERSON>", "isNullable": false, "isRecordType": false, "nameInRecord": null, "otherImports": ["package:dio/src/dio.dart"]}, "instanceName": "backendApiDio", "paramName": "_backendApiDio", "isFactoryParam": false, "isPositional": true}], "orderPosition": 0}]
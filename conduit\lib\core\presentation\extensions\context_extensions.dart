import 'package:flutter/material.dart';

extension BuildContextX on BuildContext {
  void showErrorSnackBar({required String message}) {
    // Hide any existing snackbars to prevent them from stacking
    ScaffoldMessenger.of(this).hideCurrentSnackBar();

    // Show the new snackbar
    ScaffoldMessenger.of(this).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red, // Or your app's error color
        behavior: SnackBarBehavior.floating, // Optional: makes it look nicer
      ),
    );
  }

  // You can add other helpers here too!
  void showSuccessSnackBar({required String message}) {
    ScaffoldMessenger.of(this).hideCurrentSnackBar();
    ScaffoldMessenger.of(this).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.green, // Or your app's success color
      ),
    );
  }
}

import 'package:conduit/core/services/token_service.dart';
import 'package:dio/dio.dart';
import 'package:injectable/injectable.dart';

@injectable
class AuthInterceptor extends Interceptor {
  final TokenService _tokenService;

  AuthInterceptor(this._tokenService);

  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) {
    final accessToken = _tokenService.getAccessToken();
    if (accessToken != null) {
      options.headers['Authorization'] = 'Bearer $accessToken';
      options.headers['Content-Type'] = 'application/json';
    }
    super.onRequest(options, handler);
  }

  @override
  void onError(DioException err, ErrorInterceptorHandler handler) async {
    if (err.response?.statusCode == 401) {
      try {
        await _tokenService.refreshToken();

        // Check if we got a new token
        final newToken = _tokenService.getAccessToken();
        if (newToken != null) {
          // Retry the request with new token
          final requestOptions = err.requestOptions;
          requestOptions.headers['Authorization'] = 'Bearer $newToken';

          try {
            final dio = Dio();
            final response = await dio.fetch(requestOptions);
            handler.resolve(response);
            return;
          } catch (retryError) {
            // If retry fails, continue with original error
          }
        }
      } catch (refreshError) {
        // If refresh fails, continue with original error
      }
    }
    super.onError(err, handler);
  }
}

[{"type": {"import": "package:conduit/core/network/api_clients.dart", "name": "BackendApiClient", "isNullable": false, "isRecordType": false, "nameInRecord": null}, "typeImpl": {"import": "package:conduit/core/network/api_clients.dart", "name": "BackendApiClientImpl", "isNullable": false, "isRecordType": false, "nameInRecord": null}, "isAsync": false, "postConstructReturnsSelf": false, "preResolve": false, "canBeConst": false, "injectableType": 2, "dependsOn": [], "environments": [], "dependencies": [{"type": {"import": "package:dio/dio.dart", "name": "<PERSON><PERSON>", "isNullable": false, "isRecordType": false, "nameInRecord": null, "otherImports": ["package:dio/src/dio.dart"]}, "instanceName": "backendApiDio", "paramName": "dio", "isFactoryParam": false, "isPositional": true}], "instanceName": "backendApiClient", "orderPosition": 0}]
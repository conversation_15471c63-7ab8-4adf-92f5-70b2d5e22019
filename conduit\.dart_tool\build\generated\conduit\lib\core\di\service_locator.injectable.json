[{"type": {"import": "package:firebase_auth/firebase_auth.dart", "name": "FirebaseAuth", "isNullable": false, "isRecordType": false, "nameInRecord": null}, "typeImpl": {"import": "package:firebase_auth/firebase_auth.dart", "name": "FirebaseAuth", "isNullable": false, "isRecordType": false, "nameInRecord": null}, "isAsync": false, "postConstructReturnsSelf": false, "preResolve": false, "canBeConst": false, "injectableType": 2, "moduleConfig": {"isAbstract": false, "isMethod": false, "type": {"import": "package:conduit/core/di/service_locator.dart", "name": "RegisterModule", "isNullable": false, "isRecordType": false, "nameInRecord": null}, "initializerName": "firebaseAuth"}, "dependsOn": [], "environments": [], "dependencies": [], "constructorName": "firebaseAuth", "orderPosition": 0}, {"type": {"import": "package:flutter_secure_storage/flutter_secure_storage.dart", "name": "FlutterSecureStorage", "isNullable": false, "isRecordType": false, "nameInRecord": null}, "typeImpl": {"import": "package:flutter_secure_storage/flutter_secure_storage.dart", "name": "FlutterSecureStorage", "isNullable": false, "isRecordType": false, "nameInRecord": null}, "isAsync": false, "postConstructReturnsSelf": false, "preResolve": false, "canBeConst": false, "injectableType": 2, "moduleConfig": {"isAbstract": false, "isMethod": false, "type": {"import": "package:conduit/core/di/service_locator.dart", "name": "RegisterModule", "isNullable": false, "isRecordType": false, "nameInRecord": null}, "initializerName": "flutterSecureStorage"}, "dependsOn": [], "environments": [], "dependencies": [], "constructorName": "flutterSecureStorage", "orderPosition": 0}, {"type": {"import": "package:dio/dio.dart", "name": "<PERSON><PERSON>", "isNullable": false, "isRecordType": false, "nameInRecord": null, "otherImports": ["package:dio/src/dio.dart"]}, "typeImpl": {"import": "package:dio/dio.dart", "name": "<PERSON><PERSON>", "isNullable": false, "isRecordType": false, "nameInRecord": null, "otherImports": ["package:dio/src/dio.dart"]}, "isAsync": false, "postConstructReturnsSelf": false, "preResolve": false, "canBeConst": false, "injectableType": 2, "moduleConfig": {"isAbstract": false, "isMethod": true, "type": {"import": "package:conduit/core/di/service_locator.dart", "name": "NetworkModule", "isNullable": false, "isRecordType": false, "nameInRecord": null}, "initializerName": "backendApiDio"}, "dependsOn": [], "environments": [], "dependencies": [{"type": {"import": "package:conduit/core/network/api_interceptors.dart", "name": "AuthInterceptor", "isNullable": false, "isRecordType": false, "nameInRecord": null}, "instanceName": null, "paramName": "authInterceptor", "isFactoryParam": false, "isPositional": true}], "instanceName": "backendApiDio", "constructorName": "backendApiDio", "orderPosition": 0}]
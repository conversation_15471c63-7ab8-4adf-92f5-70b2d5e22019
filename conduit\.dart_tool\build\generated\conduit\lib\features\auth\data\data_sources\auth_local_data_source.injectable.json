[{"type": {"import": "package:conduit/features/auth/data/data_sources/auth_local_data_source.dart", "name": "AuthLocalDataSource", "isNullable": false, "isRecordType": false, "nameInRecord": null}, "typeImpl": {"import": "package:conduit/features/auth/data/data_sources/auth_local_data_source.dart", "name": "AuthLocalDataSourceImpl", "isNullable": false, "isRecordType": false, "nameInRecord": null}, "isAsync": false, "postConstructReturnsSelf": false, "preResolve": false, "canBeConst": false, "injectableType": 2, "dependsOn": [], "environments": [], "dependencies": [{"type": {"import": "package:flutter_secure_storage/flutter_secure_storage.dart", "name": "FlutterSecureStorage", "isNullable": false, "isRecordType": false, "nameInRecord": null}, "instanceName": null, "paramName": "_storage", "isFactoryParam": false, "isPositional": true}], "orderPosition": 0}]
[{"type": {"import": "package:conduit/core/services/token_service.dart", "name": "TokenService", "isNullable": false, "isRecordType": false, "nameInRecord": null}, "typeImpl": {"import": "package:conduit/core/services/token_service.dart", "name": "TokenService", "isNullable": false, "isRecordType": false, "nameInRecord": null}, "isAsync": false, "postConstructReturnsSelf": false, "preResolve": false, "canBeConst": false, "injectableType": 2, "dependsOn": [], "environments": [], "dependencies": [{"type": {"import": "package:flutter_secure_storage/flutter_secure_storage.dart", "name": "FlutterSecureStorage", "isNullable": false, "isRecordType": false, "nameInRecord": null}, "instanceName": null, "paramName": "_secureStorage", "isFactoryParam": false, "isPositional": true}, {"type": {"import": "package:conduit/features/auth/domain/repositories/auth_repository.dart", "name": "AuthRepository", "isNullable": false, "isRecordType": false, "nameInRecord": null}, "instanceName": null, "paramName": "_authRepository", "isFactoryParam": false, "isPositional": true}], "orderPosition": 0}]
import 'package:equatable/equatable.dart';

enum SignInStatus { initial, loading, success, failure }

class SignInState extends Equatable {
  final SignInStatus status;
  final String email;
  final String password;
  final bool isPasswordVisible;
  final String? errorMessage;
  final bool isEmailValid;
  final bool isPasswordValid;

  const SignInState({
    this.status = SignInStatus.initial,
    this.email = '',
    this.password = '',
    this.isPasswordVisible = false,
    this.errorMessage,
    this.isEmailValid = false,
    this.isPasswordValid = false,
  });

  SignInState copyWith({
    SignInStatus? status,
    String? email,
    String? password,
    bool? isPasswordVisible,
    String? errorMessage,
    bool? isEmailValid,
    bool? isPasswordValid,
  }) {
    return SignInState(
      status: status ?? this.status,
      email: email ?? this.email,
      password: password ?? this.password,
      isPasswordVisible: isPasswordVisible ?? this.isPasswordVisible,
      errorMessage: errorMessage ?? this.errorMessage,
      isEmailValid: isEmailValid ?? this.isEmailValid,
      isPasswordValid: isPasswordValid ?? this.isPasswordValid,
    );
  }

  @override
  List<Object?> get props => [
    status,
    email,
    password,
    isPasswordVisible,
    errorMessage,
    isEmailValid,
    isPasswordValid,
  ];
}

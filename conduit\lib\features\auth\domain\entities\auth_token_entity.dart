import 'package:equatable/equatable.dart';

class AuthTokenEntity extends Equatable {
  final String accessToken;
  final String? refreshToken;
  final String? userId;
  final DateTime? expiresAt;
  final String tokenType;

  const AuthTokenEntity({
    required this.accessToken,
    this.refreshToken,
    this.userId,
    this.expiresAt,
    this.tokenType = 'Bearer',
  });

  bool get isExpired {
    if (expiresAt == null) return false;
    return DateTime.now().isAfter(expiresAt!);
  }

  bool get isValid => accessToken.isNotEmpty && !isExpired;

  Map<String, String> get authHeaders => {
    'Authorization': '$tokenType $accessToken',
    'Content-Type': 'application/json',
  };

  AuthTokenEntity copyWith({
    String? accessToken,
    String? refreshToken,
    String? userId,
    DateTime? expiresAt,
    String? tokenType,
  }) {
    return AuthTokenEntity(
      accessToken: accessToken ?? this.accessToken,
      refreshToken: refreshToken ?? this.refreshToken,
      userId: userId ?? this.userId,
      expiresAt: expiresAt ?? this.expiresAt,
      tokenType: tokenType ?? this.tokenType,
    );
  }

  @override
  List<Object?> get props => [
    accessToken,
    refreshToken,
    userId,
    expiresAt,
    tokenType,
  ];
}

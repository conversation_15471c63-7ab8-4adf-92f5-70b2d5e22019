import 'package:conduit/core/errors/failures.dart';
import 'package:dartz/dartz.dart';

// You'll need to create these domain models
import '../entities/user_entity.dart';
import '../entities/auth_token_entity.dart';

abstract class AuthRepository {
  /// Check if user is currently authenticated
  Future<Either<Failure, bool>> checkAuth();

  /// Refresh the current API token
  Future<Either<Failure, AuthTokenEntity>> refreshApiToken();

  /// Sign in with email and password
  Future<Either<Failure, UserEntity>> signInWithEmailAndPassword({
    required String email,
    required String password,
  });

  /// Sign up with email, password and full name
  Future<Either<Failure, UserEntity>> signUpWithEmailAndPassword({
    required String email,
    required String password,
    required String name,
  });

  /// Reset password via email
  Future<Either<Failure, void>> resetPassword({required String email});

  /// Authenticate with API using external token (e.g., Firebase token)
  Future<Either<Failure, AuthTokenEntity>> authenticateWithApi({
    required String token,
  });

  /// Sign in with Google
  Future<Either<Failure, UserEntity>> signInWithGoogle();

  /// Sign in with Apple
  Future<Either<Failure, UserEntity>> signInWithApple();

  /// Store API token locally (secure storage)
  Future<Either<Failure, void>> storeApiToken({
    required String accessToken,
    String? refreshToken,
    String? userId,
  });

  /// Retrieve stored API token
  Future<Either<Failure, AuthTokenEntity?>> getStoredTokens();

  /// Clear all stored authentication data
  Future<Either<Failure, void>> clearAuthData();

  /// Sign out user
  Future<Either<Failure, void>> signOut();
}

import 'package:conduit/core/errors/failures.dart';
import 'package:conduit/features/auth/domain/entities/auth_token_entity.dart';
import 'package:dartz/dartz.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:injectable/injectable.dart';

abstract class AuthLocalDataSource {
  Future<Either<Failure, void>> storeTokens(AuthTokenEntity tokens);
  Future<Either<Failure, AuthTokenEntity?>> getTokens();
  Future<Either<Failure, void>> clearTokens();
}

const String _accessTokenKey = 'access_token';
const String _refreshTokenKey = 'refresh_token';

@LazySingleton(as: AuthLocalDataSource)
class AuthLocalDataSourceImpl implements AuthLocalDataSource {
  final FlutterSecureStorage _storage;

  AuthLocalDataSourceImpl(this._storage);

  @override
  Future<Either<Failure, void>> clearTokens() async {
    try {
      await _storage.delete(key: _accessTokenKey);
      await _storage.delete(key: _refreshTokenKey);
      return const Right(null);
    } catch (e) {
      return Left(CacheFailure(message: 'Failed to clear local storage.'));
    }
  }

  @override
  Future<Either<Failure, AuthTokenEntity?>> getTokens() async {
    try {
      final accessToken = await _storage.read(key: _accessTokenKey);
      if (accessToken == null) {
        return const Right(null); // Not a failure, just no token.
      }
      final refreshToken = await _storage.read(key: _refreshTokenKey);
      return Right(
        AuthTokenEntity(accessToken: accessToken, refreshToken: refreshToken),
      );
    } catch (e) {
      return Left(CacheFailure(message: 'Failed to read from local storage.'));
    }
  }

  @override
  Future<Either<Failure, void>> storeTokens(AuthTokenEntity tokens) async {
    try {
      await _storage.write(key: _accessTokenKey, value: tokens.accessToken);
      if (tokens.refreshToken != null) {
        await _storage.write(
          key: _refreshTokenKey,
          value: tokens.refreshToken!,
        );
      }
      return const Right(null);
    } catch (e) {
      return Left(CacheFailure(message: 'Failed to write to local storage.'));
    }
  }
}

[{"type": {"import": "package:conduit/features/auth/presentation/bloc/reset_password/reset_password_bloc.dart", "name": "ResetPasswordBloc", "isNullable": false, "isRecordType": false, "nameInRecord": null}, "typeImpl": {"import": "package:conduit/features/auth/presentation/bloc/reset_password/reset_password_bloc.dart", "name": "ResetPasswordBloc", "isNullable": false, "isRecordType": false, "nameInRecord": null}, "isAsync": false, "postConstructReturnsSelf": false, "preResolve": false, "canBeConst": false, "injectableType": 0, "dependsOn": [], "environments": [], "dependencies": [{"type": {"import": "package:conduit/features/auth/domain/use_cases/reset_password_use_case.dart", "name": "ResetPasswordUseCase", "isNullable": false, "isRecordType": false, "nameInRecord": null}, "instanceName": null, "paramName": "resetPasswordUseCase", "isFactoryParam": false, "isPositional": false}], "orderPosition": 0}]
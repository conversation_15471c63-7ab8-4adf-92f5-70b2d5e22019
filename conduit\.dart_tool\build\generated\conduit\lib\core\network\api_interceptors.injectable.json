[{"type": {"import": "package:conduit/core/network/api_interceptors.dart", "name": "AuthInterceptor", "isNullable": false, "isRecordType": false, "nameInRecord": null}, "typeImpl": {"import": "package:conduit/core/network/api_interceptors.dart", "name": "AuthInterceptor", "isNullable": false, "isRecordType": false, "nameInRecord": null}, "isAsync": false, "postConstructReturnsSelf": false, "preResolve": false, "canBeConst": false, "injectableType": 0, "dependsOn": [], "environments": [], "dependencies": [{"type": {"import": "package:conduit/core/services/token_service.dart", "name": "TokenService", "isNullable": false, "isRecordType": false, "nameInRecord": null}, "instanceName": null, "paramName": "_tokenService", "isFactoryParam": false, "isPositional": true}], "orderPosition": 0}]
[{"type": {"import": "package:conduit/features/auth/domain/use_cases/sign_in_with_google_use_case.dart", "name": "SignInWithGoogleUseCase", "isNullable": false, "isRecordType": false, "nameInRecord": null}, "typeImpl": {"import": "package:conduit/features/auth/domain/use_cases/sign_in_with_google_use_case.dart", "name": "SignInWithGoogleUseCase", "isNullable": false, "isRecordType": false, "nameInRecord": null}, "isAsync": false, "postConstructReturnsSelf": false, "preResolve": false, "canBeConst": false, "injectableType": 0, "dependsOn": [], "environments": [], "dependencies": [{"type": {"import": "package:conduit/features/auth/domain/repositories/auth_repository.dart", "name": "AuthRepository", "isNullable": false, "isRecordType": false, "nameInRecord": null}, "instanceName": null, "paramName": "authRepository", "isFactoryParam": false, "isPositional": true}], "orderPosition": 0}]
[{"type": {"import": "package:conduit/features/auth/presentation/bloc/signup/signup_bloc.dart", "name": "SignUpBloc", "isNullable": false, "isRecordType": false, "nameInRecord": null}, "typeImpl": {"import": "package:conduit/features/auth/presentation/bloc/signup/signup_bloc.dart", "name": "SignUpBloc", "isNullable": false, "isRecordType": false, "nameInRecord": null}, "isAsync": false, "postConstructReturnsSelf": false, "preResolve": false, "canBeConst": false, "injectableType": 0, "dependsOn": [], "environments": [], "dependencies": [{"type": {"import": "package:conduit/features/auth/domain/use_cases/sign_up_with_email_and_password_use_case.dart", "name": "SignUpWithEmailAndPasswordUseCase", "isNullable": false, "isRecordType": false, "nameInRecord": null}, "instanceName": null, "paramName": "signUpWithEmailAndPasswordUseCase", "isFactoryParam": false, "isPositional": false}, {"type": {"import": "package:conduit/features/auth/domain/use_cases/sign_in_with_apple_use_case.dart", "name": "SignInWithAppleUseCase", "isNullable": false, "isRecordType": false, "nameInRecord": null}, "instanceName": null, "paramName": "signInWithAppleUseCase", "isFactoryParam": false, "isPositional": false}, {"type": {"import": "package:conduit/features/auth/domain/use_cases/sign_in_with_google_use_case.dart", "name": "SignInWithGoogleUseCase", "isNullable": false, "isRecordType": false, "nameInRecord": null}, "instanceName": null, "paramName": "signInWithGoogleUseCase", "isFactoryParam": false, "isPositional": false}], "orderPosition": 0}]
import 'package:conduit/features/auth/domain/repositories/auth_repository.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:injectable/injectable.dart';

@lazySingleton
class TokenService {
  final FlutterSecureStorage _secureStorage;
  final AuthRepository _authRepository;

  static const String _accessTokenKey = 'access_token';
  static const String _refreshTokenKey = 'refresh_token';
  String? _cachedToken;

  TokenService(this._secureStorage, this._authRepository);

  // Initialize cache when app starts
  Future<void> initialize() async {
    try {
      _cachedToken = await _secureStorage.read(key: _accessTokenKey);
    } catch (e) {
      // Log error but don't throw - app should still work without cached token
      print('Error initializing token cache: $e');
      _cachedToken = null;
    }
  }

  // Synchronous token getter for interceptor
  String? getAccessToken() => _cachedToken;

  // Update token in cache and storage
  Future<void> updateToken(String? token) async {
    try {
      _cachedToken = token;
      if (token != null) {
        await _secureStorage.write(key: _accessTokenKey, value: token);
      } else {
        await _secureStorage.delete(key: _accessTokenKey);
      }
    } catch (e) {
      print('Error updating token: $e');
      // Keep cached token even if storage fails
    }
  }

  // Get refresh token from storage
  Future<String?> getRefreshToken() async {
    try {
      return await _secureStorage.read(key: _refreshTokenKey);
    } catch (e) {
      print('Error reading refresh token: $e');
      return null;
    }
  }

  // Update refresh token in storage
  Future<void> updateRefreshToken(String? refreshToken) async {
    try {
      if (refreshToken != null) {
        await _secureStorage.write(key: _refreshTokenKey, value: refreshToken);
      } else {
        await _secureStorage.delete(key: _refreshTokenKey);
      }
    } catch (e) {
      print('Error updating refresh token: $e');
    }
  }

  // Refresh token
  Future<void> refreshToken() async {
    try {
      final result = await _authRepository.refreshApiToken();
      result.fold(
        (failure) async {
          // Clear tokens on refresh failure
          await clearAllTokens();
          await _authRepository.clearAuthData();
        },
        (newToken) async {
          // Update with new token
          await updateToken(newToken.accessToken);
          if (newToken.refreshToken != null) {
            await updateRefreshToken(newToken.refreshToken);
          }
        },
      );
    } catch (e) {
      print('Error refreshing token: $e');
      await clearAllTokens();
    }
  }

  // Clear token from cache only (synchronous)
  void clearToken() {
    _cachedToken = null;
  }

  // Clear all tokens from both cache and storage
  Future<void> clearAllTokens() async {
    try {
      _cachedToken = null;
      await Future.wait([
        _secureStorage.delete(key: _accessTokenKey),
        _secureStorage.delete(key: _refreshTokenKey),
      ]);
    } catch (e) {
      print('Error clearing tokens: $e');
    }
  }

  // Check if we have a valid cached token
  bool get hasValidToken => _cachedToken != null && _cachedToken!.isNotEmpty;

  // Get all stored auth keys (useful for debugging)
  Future<Map<String, String>> getAllAuthData() async {
    try {
      final allData = await _secureStorage.readAll();
      return Map.fromEntries(
        allData.entries.where(
          (entry) =>
              entry.key == _accessTokenKey || entry.key == _refreshTokenKey,
        ),
      );
    } catch (e) {
      print('Error reading all auth data: $e');
      return {};
    }
  }
}
